import { Button, ButtonText } from '@indie-points/ui-button';
import { HStack } from '@indie-points/ui-hstack';
import { VStack } from '@indie-points/ui-vstack';
import * as Haptics from 'expo-haptics';
import React from 'react';

import { FacebookIcon } from './FacebookIcon';
import { GoogleIcon } from './GoogleIcon';
import { TwitterIcon } from './TwitterIcon';

interface SocialButtonProps {
  onPress: () => void;
  children: React.ReactNode;
  variant?: 'outline' | 'solid';
}

const SocialButton = ({
  onPress,
  children,
  variant = 'outline',
}: SocialButtonProps) => (
  <Button
    variant={variant}
    size='lg'
    className='w-full bg-background-0 border-2 border-outline-300 rounded-xl shadow-sm'
    onPress={async () => {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      onPress();
    }}
  >
    <HStack space='sm' className='items-center'>
      {children}
    </HStack>
  </Button>
);

interface SocialLoginButtonsProps {
  onSocialLogin: (provider: 'google' | 'facebook' | 'twitter') => void;
}

export function SocialLoginButtons({ onSocialLogin }: SocialLoginButtonsProps) {
  return (
    <VStack space='md'>
      <SocialButton onPress={() => onSocialLogin('google')}>
        <GoogleIcon size={24} />
        <ButtonText className='text-typography-900 font-medium'>
          Continue with Google
        </ButtonText>
      </SocialButton>

      <SocialButton onPress={() => onSocialLogin('facebook')}>
        <FacebookIcon size={24} />
        <ButtonText className='text-typography-900 font-medium'>
          Continue with Facebook
        </ButtonText>
      </SocialButton>

      <SocialButton onPress={() => onSocialLogin('twitter')}>
        <TwitterIcon size={24} />
        <ButtonText className='text-typography-900 font-medium'>
          Continue with Twitter
        </ButtonText>
      </SocialButton>
    </VStack>
  );
}
