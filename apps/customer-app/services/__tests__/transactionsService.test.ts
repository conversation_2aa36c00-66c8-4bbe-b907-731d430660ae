import { supabase } from '@indie-points/lib';

import { TransactionsService } from '../transactionsService';

// Mock the supabase client
jest.mock('@indie-points/lib', () => ({
  supabase: {
    rpc: jest.fn(),
  },
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('TransactionsService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getCustomerTransactionHistory', () => {
    it('should return transaction data when successful', async () => {
      const mockData = [
        {
          transaction_id: 1,
          business_id: 1,
          transaction_type: 'PURCHASE',
          business_name: 'StuMac Solutions',
          business_type: 'Gym/Fitness',
          points_awarded: 463,
          points_redeemed: 0,
          amount_spent: '£463.00 spent',
          created_at: '11 Jun 2025, 16:37',
        },
        {
          transaction_id: 2,
          business_id: 1,
          transaction_type: 'REDEMPTION',
          business_name: 'StuMac Solutions',
          business_type: 'Gym/Fitness',
          points_awarded: 0,
          points_redeemed: 740,
          amount_spent: '',
          created_at: '11 Jun 2025, 16:37',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        count: 2,
        data: mockData,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await TransactionsService.getCustomerTransactionHistory('test-user-id');

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'get_customer_transaction_history',
        {
          p_customer_id: 'test-user-id',
          p_page: 1,
          p_page_size: 100,
        }
      );

      expect(result).toEqual({
        data: [
          {
            id: 1,
            businessId: 1,
            type: 'purchase',
            businessName: 'StuMac Solutions',
            businessCategory: 'Gym/Fitness',
            pointsEarned: 463,
            pointsRedeemed: 0,
            amount: '£463.00 spent',
            date: '11 Jun 2025, 16:37',
          },
          {
            id: 2,
            businessId: 1,
            type: 'redemption',
            businessName: 'StuMac Solutions',
            businessCategory: 'Gym/Fitness',
            pointsEarned: 0,
            pointsRedeemed: 740,
            amount: '',
            date: '11 Jun 2025, 16:37',
          },
        ],
        error: null,
      });
    });

    it('should handle supabase errors', async () => {
      const mockError = {
        message: 'Database connection failed',
        code: '500',
        hint: 'Check your database connection',
        details: 'Connection refused',
        name: 'DatabaseError',
      };

      mockSupabase.rpc.mockResolvedValue({
        count: null,
        data: null,
        error: mockError,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const result =
        await TransactionsService.getCustomerTransactionHistory('test-user-id');

      expect(result).toEqual({
        data: null,
        error: 'Database connection failed',
      });
    });

    it('should handle empty data response', async () => {
      mockSupabase.rpc.mockResolvedValue({
        count: 0,
        data: [],
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await TransactionsService.getCustomerTransactionHistory('test-user-id');

      expect(result).toEqual({
        data: [],
        error: null,
      });
    });

    it('should handle unexpected errors', async () => {
      mockSupabase.rpc.mockRejectedValue(new Error('Network error'));

      const result =
        await TransactionsService.getCustomerTransactionHistory('test-user-id');

      expect(result).toEqual({
        data: null,
        error: 'Network error',
      });
    });

    it('should handle null data response', async () => {
      mockSupabase.rpc.mockResolvedValue({
        count: 0,
        data: null,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await TransactionsService.getCustomerTransactionHistory('test-user-id');

      expect(result).toEqual({
        data: [],
        error: null,
      });
    });
  });

  describe('getCustomerTransactionHistoryForBusiness', () => {
    it('should return transaction data for specific business when successful', async () => {
      const mockData = [
        {
          transaction_id: 1,
          business_id: 1,
          transaction_type: 'PURCHASE',
          business_name: 'StuMac Solutions',
          business_type: 'Gym/Fitness',
          points_awarded: 463,
          points_redeemed: 0,
          amount_spent: '£463.00 spent',
          created_at: '11 Jun 2025, 16:37',
        },
        {
          transaction_id: 3,
          business_id: 1,
          transaction_type: 'REDEMPTION',
          business_name: 'StuMac Solutions',
          business_type: 'Gym/Fitness',
          points_awarded: 0,
          points_redeemed: 200,
          amount_spent: '',
          created_at: '12 Jun 2025, 10:15',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        count: 2,
        data: mockData,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await TransactionsService.getCustomerTransactionHistoryForBusiness(
          'test-user-id',
          '1'
        );

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'get_customer_transaction_history_for_business',
        {
          p_customer_id: 'test-user-id',
          p_business_id: '1',
          p_page: 1,
          p_page_size: 100,
        }
      );

      expect(result).toEqual({
        data: [
          {
            id: 1,
            businessId: 1,
            type: 'purchase',
            businessName: 'StuMac Solutions',
            businessCategory: 'Gym/Fitness',
            pointsEarned: 463,
            pointsRedeemed: 0,
            amount: '£463.00 spent',
            date: '11 Jun 2025, 16:37',
          },
          {
            id: 3,
            businessId: 1,
            type: 'redemption',
            businessName: 'StuMac Solutions',
            businessCategory: 'Gym/Fitness',
            pointsEarned: 0,
            pointsRedeemed: 200,
            amount: '',
            date: '12 Jun 2025, 10:15',
          },
        ],
        error: null,
      });
    });

    it('should return error when Supabase returns an error', async () => {
      const mockError = {
        message: 'Database connection failed',
        code: '500',
        hint: 'Check your database connection',
        details: 'Connection refused',
        name: 'DatabaseError',
      };

      mockSupabase.rpc.mockResolvedValue({
        count: null,
        data: null,
        error: mockError,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const result =
        await TransactionsService.getCustomerTransactionHistoryForBusiness(
          'test-user-id',
          '1'
        );

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'get_customer_transaction_history_for_business',
        {
          p_customer_id: 'test-user-id',
          p_business_id: '1',
          p_page: 1,
          p_page_size: 100,
        }
      );

      expect(result).toEqual({
        data: null,
        error: 'Database connection failed',
      });
    });

    it('should return empty array when no data is returned', async () => {
      mockSupabase.rpc.mockResolvedValue({
        count: 0,
        data: [],
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await TransactionsService.getCustomerTransactionHistoryForBusiness(
          'test-user-id',
          '1'
        );

      expect(result).toEqual({
        data: [],
        error: null,
      });
    });
  });

  describe('getCustomerBusinessSummaries', () => {
    it('should return business data when successful', async () => {
      const mockData = [
        {
          business_id: 1,
          business_name: 'StuMac Solutions',
          business_type: 'Gym/Fitness',
          active_points: 759,
          last_transaction_date: '11 Jun 2025',
        },
        {
          business_id: 2,
          business_name: 'Indie Points',
          business_type: 'System',
          active_points: 1,
          last_transaction_date: '11 Jun 2025',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        count: 2,
        data: mockData,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await TransactionsService.getCustomerBusinessSummaries('test-user-id');

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'get_customer_business_summaries',
        {
          p_customer_id: 'test-user-id',
        }
      );

      expect(result).toEqual({
        data: [
          {
            id: 1,
            name: 'StuMac Solutions',
            category: 'Gym/Fitness',
            points: 759,
            lastVisit: '11 Jun 2025',
          },
          {
            id: 2,
            name: 'Indie Points',
            category: 'System',
            points: 1,
            lastVisit: '11 Jun 2025',
          },
        ],
        error: null,
      });
    });

    it('should handle supabase errors', async () => {
      const mockError = {
        message: 'Database connection failed',
        code: '500',
        hint: 'Check your database connection',
        details: 'Connection refused',
        name: 'DatabaseError',
      };

      mockSupabase.rpc.mockResolvedValue({
        count: null,
        data: null,
        error: mockError,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const result =
        await TransactionsService.getCustomerBusinessSummaries('test-user-id');

      expect(result).toEqual({
        data: null,
        error: 'Database connection failed',
      });
    });

    it('should handle empty data response', async () => {
      mockSupabase.rpc.mockResolvedValue({
        count: 0,
        data: [],
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await TransactionsService.getCustomerBusinessSummaries('test-user-id');

      expect(result).toEqual({
        data: [],
        error: null,
      });
    });

    it('should handle unexpected errors', async () => {
      mockSupabase.rpc.mockRejectedValue(new Error('Network error'));

      const result =
        await TransactionsService.getCustomerBusinessSummaries('test-user-id');

      expect(result).toEqual({
        data: null,
        error: 'Network error',
      });
    });

    it('should handle missing values in response data', async () => {
      const mockData = [
        {
          business_id: null,
          business_name: undefined,
          business_type: 'Test Category',
          active_points: null,
          last_transaction_date: '2025-06-17',
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        count: 1,
        data: mockData,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await TransactionsService.getCustomerBusinessSummaries('test-user-id');

      expect(result).toEqual({
        data: [
          {
            id: null,
            name: undefined,
            category: 'Test Category',
            points: null,
            lastVisit: '2025-06-17',
          },
        ],
        error: null,
      });
    });

    it('should handle null data response', async () => {
      mockSupabase.rpc.mockResolvedValue({
        count: 0,
        data: null,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result =
        await TransactionsService.getCustomerBusinessSummaries('test-user-id');

      expect(result).toEqual({
        data: [],
        error: null,
      });
    });
  });

  describe('createVisitTransaction', () => {
    it('should return visit transaction data when successful', async () => {
      const mockData = [
        {
          transaction_id: 'txn-123',
          customer_id: 'customer-123',
          business_id: 'business-123',
          amount_spent: 0,
          points_awarded: 1,
          points_redeemed: 0,
          qr_token: 'qr-token-123',
          type: 'VISIT',
          created_at: '2025-06-18T10:00:00Z',
          updated_at: '2025-06-18T10:00:00Z',
          already_exists: false,
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        count: 1,
        data: mockData,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result = await TransactionsService.createVisitTransaction(
        'customer-123',
        'business-123',
        'Test Business',
        'qr-token-123'
      );

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'create_visit_transaction',
        {
          p_customer_id: 'customer-123',
          p_business_id: 'business-123',
          p_qr_token: 'qr-token-123',
          p_business_name: 'Test Business',
        }
      );

      expect(result).toEqual({
        data: {
          alreadyExists: false,
        },
        error: null,
      });
    });

    it('should return visit transaction data when already exists', async () => {
      const mockData = [
        {
          transaction_id: 'txn-456',
          customer_id: 'customer-123',
          business_id: 'business-123',
          amount_spent: 0,
          points_awarded: 1,
          points_redeemed: 0,
          qr_token: 'qr-token-123',
          type: 'VISIT',
          created_at: '2025-06-18T09:00:00Z',
          updated_at: '2025-06-18T09:00:00Z',
          already_exists: true,
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        count: 1,
        data: mockData,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result = await TransactionsService.createVisitTransaction(
        'customer-123',
        'business-123',
        'Test Business',
        'qr-token-123'
      );

      expect(result).toEqual({
        data: {
          alreadyExists: true,
        },
        error: null,
      });
    });

    it('should return error when Supabase returns an error', async () => {
      const mockError = {
        message: 'Database connection failed',
        code: '500',
        hint: 'Check your database connection',
        details: 'Connection refused',
        name: 'DatabaseError',
      };

      mockSupabase.rpc.mockResolvedValue({
        count: null,
        data: null,
        error: mockError,
        status: 500,
        statusText: 'Internal Server Error',
      });

      const result = await TransactionsService.createVisitTransaction(
        'customer-123',
        'business-123',
        'Test Business',
        'qr-token-123'
      );

      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'create_visit_transaction',
        {
          p_customer_id: 'customer-123',
          p_business_id: 'business-123',
          p_qr_token: 'qr-token-123',
          p_business_name: 'Test Business',
        }
      );

      expect(result).toEqual({
        data: null,
        error: 'Database connection failed',
      });
    });

    it('should return error when no transaction data is returned', async () => {
      mockSupabase.rpc.mockResolvedValue({
        count: 0,
        data: [],
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result = await TransactionsService.createVisitTransaction(
        'customer-123',
        'business-123',
        'qr-token-123',
        'Test Business'
      );

      expect(result).toEqual({
        data: null,
        error: 'No transaction data returned',
      });
    });

    it('should return error when data is null', async () => {
      mockSupabase.rpc.mockResolvedValue({
        count: 0,
        data: null,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result = await TransactionsService.createVisitTransaction(
        'customer-123',
        'business-123',
        'qr-token-123',
        'Test Business'
      );

      expect(result).toEqual({
        data: null,
        error: 'No transaction data returned',
      });
    });

    it('should handle unexpected errors', async () => {
      mockSupabase.rpc.mockRejectedValue(new Error('Network error'));

      const result = await TransactionsService.createVisitTransaction(
        'customer-123',
        'business-123',
        'qr-token-123',
        'Test Business'
      );

      expect(result).toEqual({
        data: null,
        error: 'Network error',
      });
    });

    it('should handle non-Error exceptions', async () => {
      mockSupabase.rpc.mockRejectedValue('String error');

      const result = await TransactionsService.createVisitTransaction(
        'customer-123',
        'business-123',
        'qr-token-123',
        'Test Business'
      );

      expect(result).toEqual({
        data: null,
        error: 'An unexpected error occurred',
      });
    });

    it('should handle missing values in response data', async () => {
      const mockData = [
        {
          transaction_id: null,
          customer_id: undefined,
          business_id: 'business-123',
          amount_spent: 0,
          points_awarded: 1,
          points_redeemed: 0,
          qr_token: 'qr-token-123',
          type: 'VISIT',
          created_at: '2025-06-18T10:00:00Z',
          updated_at: '2025-06-18T10:00:00Z',
          already_exists: false,
        },
      ];

      mockSupabase.rpc.mockResolvedValue({
        count: 1,
        data: mockData,
        error: null,
        status: 200,
        statusText: 'OK',
      });

      const result = await TransactionsService.createVisitTransaction(
        'customer-123',
        'business-123',
        'qr-token-123',
        'Test Business'
      );

      expect(result).toEqual({
        data: {
          alreadyExists: false,
        },
        error: null,
      });
    });
  });
});
