import FontAwesome from '@expo/vector-icons/FontAwesome';
import { GradientBar } from '@indie-points/auth';
import { useAuth } from '@indie-points/contexts';
import { Box } from '@indie-points/ui-box';
import { Heading } from '@indie-points/ui-heading';
import { HStack } from '@indie-points/ui-hstack';
import { Pressable } from '@indie-points/ui-pressable';
import { RefreshControl } from '@indie-points/ui-refresh-control';
import { Text } from '@indie-points/ui-text';
import { VStack } from '@indie-points/ui-vstack';
import dayjs from 'dayjs';
import * as Haptics from 'expo-haptics';
import { useRouter } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, ScrollView } from 'react-native';

import {
  CustomerBusinessSummary,
  CustomerTransaction,
  TransactionsService,
} from '../../../services';

export default function History() {
  const { user } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'transactions' | 'businesses'>(
    'businesses'
  );
  const [transactionData, setTransactionData] = useState<CustomerTransaction[]>(
    []
  );
  const [businessData, setBusinessData] = useState<CustomerBusinessSummary[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Pagination state for transactions
  const [transactionPage, setTransactionPage] = useState(1);
  const [loadingMoreTransactions, setLoadingMoreTransactions] = useState(false);
  const [hasMoreTransactions, setHasMoreTransactions] = useState(true);
  const PAGE_SIZE = 10; // Smaller page size for better UX

  const fetchTransactions = useCallback(
    async (page: number, isRefresh = false) => {
      if (!user?.id) return;

      if (page === 1) {
        if (!isRefresh) setLoading(true);
        setError(null);
      } else {
        setLoadingMoreTransactions(true);
      }

      try {
        const result = await TransactionsService.getCustomerTransactionHistory(
          user.id,
          page,
          PAGE_SIZE
        );

        if (result.error) {
          setError(result.error);
          if (page === 1 && !isRefresh) setLoading(false);
          if (page > 1) setLoadingMoreTransactions(false);
          return;
        }

        const newTransactions = result.data || [];

        if (page === 1) {
          // First page - replace all data
          setTransactionData(newTransactions);
        } else {
          // Subsequent pages - append data
          setTransactionData(prev => [...prev, ...newTransactions]);
        }

        // Check if we have more data
        setHasMoreTransactions(newTransactions.length === PAGE_SIZE);

        if (page === 1 && !isRefresh) setLoading(false);
        if (page > 1) setLoadingMoreTransactions(false);
      } catch (err) {
        setError('An unexpected error occurred while fetching transactions');
        console.error('Error fetching transactions:', err);
        if (page === 1 && !isRefresh) setLoading(false);
        if (page > 1) setLoadingMoreTransactions(false);
      }
    },
    [user?.id]
  );

  const fetchBusinesses = useCallback(
    async (isRefresh = false) => {
      if (!user?.id) return;

      if (!isRefresh) setLoading(true);
      setError(null);

      try {
        const result = await TransactionsService.getCustomerBusinessSummaries(
          user.id
        );

        if (result.error) {
          setError(result.error);
          if (!isRefresh) setLoading(false);
          return;
        }

        setBusinessData(result.data || []);
        if (!isRefresh) setLoading(false);
      } catch (err) {
        setError('An unexpected error occurred while fetching businesses');
        console.error('Error fetching businesses:', err);
        if (!isRefresh) setLoading(false);
      }
    },
    [user?.id]
  );

  const fetchData = useCallback(
    async (isRefresh = false) => {
      if (!user?.id) {
        if (!isRefresh) setLoading(false);
        return;
      }

      try {
        // Reset pagination state
        if (isRefresh) {
          setTransactionPage(1);
          setHasMoreTransactions(true);
        }

        // Fetch data based on active tab
        if (activeTab === 'transactions') {
          await fetchTransactions(1, isRefresh);
        } else {
          await fetchBusinesses(isRefresh);
        }
      } catch (err) {
        setError('An unexpected error occurred while fetching data');
        console.error('Error fetching history data:', err);
        if (!isRefresh) setLoading(false);
      }
    },
    [user?.id, activeTab, fetchTransactions, fetchBusinesses]
  );

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Handle tab change
  useEffect(() => {
    if (user?.id) {
      if (activeTab === 'transactions') {
        fetchTransactions(1);
      } else {
        fetchBusinesses();
      }
    }
  }, [activeTab, user?.id, fetchTransactions, fetchBusinesses]);

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      fetchData(true),
      new Promise(resolve => setTimeout(resolve, 750)),
    ]);
    setRefreshing(false);
  };

  const loadMoreTransactions = async () => {
    if (loadingMoreTransactions || !hasMoreTransactions || !user?.id) return;

    const nextPage = transactionPage + 1;
    setTransactionPage(nextPage);
    await fetchTransactions(nextPage);
  };

  const handleTransactionPress = (transaction: CustomerTransaction) => {
    // Navigate to business history page with business details
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push({
      pathname: '/history/business-history',
      params: {
        businessId: transaction.businessId.toString(),
        businessName: transaction.businessName,
        businessCategory: transaction.businessCategory,
      },
    });
  };

  const handleBusinessPress = (business: CustomerBusinessSummary) => {
    // Navigate to business history page with business details
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push({
      pathname: '/history/business-history',
      params: {
        businessId: business.id.toString(),
        businessName: business.name,
        businessCategory: business.category,
      },
    });
  };

  const renderTransactionItem = (item: CustomerTransaction) => (
    <Pressable
      key={item.id}
      className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-4 active:bg-background-50'
      onPress={() => handleTransactionPress(item)}
    >
      <HStack space='md' className='items-center'>
        {/* Icon */}
        <Box
          className={`w-12 h-12 rounded-xl items-center justify-center border-2 ${
            item.type === 'purchase'
              ? 'bg-primary-500 border-primary-700'
              : item.type === 'redemption'
                ? 'bg-error-500 border-error-700'
                : 'bg-secondary-500 border-secondary-700'
          }`}
        >
          <FontAwesome
            name={
              item.type === 'purchase'
                ? 'shopping-bag'
                : item.type === 'redemption'
                  ? 'gift'
                  : 'eye'
            }
            size={20}
            color='white'
          />
        </Box>

        {/* Content */}
        <VStack className='flex-1'>
          <Text size='md' className='text-typography-900 font-semibold'>
            {item.businessName}
          </Text>
          <Text size='sm' className='text-typography-600'>
            {item.type === 'purchase'
              ? 'Purchase'
              : item.type === 'redemption'
                ? 'Redemption'
                : 'Visit'}{' '}
            • {item.businessCategory}
          </Text>
          <Text size='xs' className='text-typography-500'>
            {dayjs(item.date).format('DD MMM YYYY, HH:mm')}
          </Text>
        </VStack>

        {/* Points and Arrow */}
        <VStack className='items-end'>
          <Text
            size='lg'
            className={`font-bold ${
              item.type === 'redemption' ? 'text-error-500' : 'text-primary-500'
            }`}
          >
            {item.type === 'redemption'
              ? `-${item.pointsRedeemed ?? 0} pts`
              : `${item.pointsEarned > 0 ? '+' : ''}${item.pointsEarned} pts`}
          </Text>
          <FontAwesome name='chevron-right' size={12} color='#9CA3AF' />
        </VStack>
      </HStack>
    </Pressable>
  );

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        onScroll={({ nativeEvent }) => {
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const paddingToBottom = 20;

          if (
            layoutMeasurement.height + contentOffset.y >=
            contentSize.height - paddingToBottom
          ) {
            // User has scrolled to the bottom
            if (activeTab === 'transactions') {
              loadMoreTransactions();
            }
          }
        }}
        scrollEventThrottle={400}
      >
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            History
          </Heading>

          {/* Colored divider line */}
          <GradientBar />

          {/* Tab Navigation */}
          <HStack className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg overflow-hidden'>
            <Pressable
              className={`flex-1 py-3 px-6 ${
                activeTab === 'businesses' ? 'bg-primary-500' : 'bg-white'
              }`}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                setActiveTab('businesses');
              }}
            >
              <Text
                size='md'
                className={`font-semibold text-center ${
                  activeTab === 'businesses'
                    ? 'text-white'
                    : 'text-typography-900'
                }`}
              >
                Businesses
              </Text>
            </Pressable>
            <Pressable
              className={`flex-1 py-3 px-6 ${
                activeTab === 'transactions' ? 'bg-primary-500' : 'bg-white'
              }`}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                setActiveTab('transactions');
              }}
            >
              <Text
                size='md'
                className={`font-semibold text-center ${
                  activeTab === 'transactions'
                    ? 'text-white'
                    : 'text-typography-900'
                }`}
              >
                Transactions
              </Text>
            </Pressable>
          </HStack>
        </VStack>

        <Box className='px-6 pb-8'>
          {loading && !refreshing ? (
            <Box className='flex-1 items-center justify-center py-12'>
              <ActivityIndicator size='large' color='#3B82F6' />
              <Text size='md' className='text-typography-600 mt-4'>
                Loading history...
              </Text>
            </Box>
          ) : error ? (
            <Box className='bg-error-50 border-2 border-error-500 rounded-2xl p-4'>
              <HStack space='md' className='items-center'>
                <FontAwesome
                  name='exclamation-triangle'
                  size={20}
                  color='#EF4444'
                />
                <VStack className='flex-1'>
                  <Text size='md' className='text-error-700 font-semibold'>
                    Error loading data
                  </Text>
                  <Text size='sm' className='text-error-600'>
                    {error}
                  </Text>
                </VStack>
              </HStack>
            </Box>
          ) : activeTab === 'transactions' ? (
            <VStack space='md'>
              {transactionData.length === 0 ? (
                <Box className='bg-background-50 border-2 border-background-300 rounded-2xl p-6 items-center'>
                  <FontAwesome name='history' size={40} color='#9CA3AF' />
                  <Text
                    size='md'
                    className='text-typography-600 mt-4 text-center'
                  >
                    No transactions found
                  </Text>
                  <Text
                    size='sm'
                    className='text-typography-500 mt-2 text-center'
                  >
                    Your transaction history will appear here once you start
                    earning points
                  </Text>
                </Box>
              ) : (
                <>
                  {transactionData.map(renderTransactionItem)}

                  {/* Loading more indicator */}
                  {loadingMoreTransactions && (
                    <Box className='py-4 items-center'>
                      <ActivityIndicator size='small' color='#3B82F6' />
                      <Text size='sm' className='text-typography-600 mt-2'>
                        Loading more transactions...
                      </Text>
                    </Box>
                  )}

                  {/* End of list indicator */}
                  {!hasMoreTransactions && transactionData.length > 0 && (
                    <Box className='py-4 items-center'>
                      <Text size='sm' className='text-typography-500'>
                        You have reached the end of your transaction history
                      </Text>
                    </Box>
                  )}
                </>
              )}
            </VStack>
          ) : (
            <VStack space='md'>
              {businessData.length === 0 ? (
                <Box className='bg-background-50 border-2 border-background-300 rounded-2xl p-6 items-center'>
                  <FontAwesome name='building' size={40} color='#9CA3AF' />
                  <Text
                    size='md'
                    className='text-typography-600 mt-4 text-center'
                  >
                    No businesses found
                  </Text>
                  <Text
                    size='sm'
                    className='text-typography-500 mt-2 text-center'
                  >
                    Businesses you visit will appear here
                  </Text>
                </Box>
              ) : (
                businessData.map(business => (
                  <Pressable
                    key={business.id}
                    className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-4 active:bg-background-50'
                    onPress={() => handleBusinessPress(business)}
                  >
                    <HStack space='md' className='items-center'>
                      {/* Icon */}
                      <Box className='w-12 h-12 bg-primary-500 border-2 border-primary-700 rounded-xl items-center justify-center'>
                        <FontAwesome name='building' size={20} color='white' />
                      </Box>

                      {/* Content */}
                      <VStack className='flex-1'>
                        <Text
                          size='md'
                          className='text-typography-900 font-semibold'
                        >
                          {business.name}
                        </Text>
                        <Text size='sm' className='text-typography-600'>
                          {business.category}
                        </Text>
                        <Text size='xs' className='text-typography-500'>
                          Last visit:{' '}
                          {dayjs(business.lastVisit).format(
                            'DD MMM YYYY, HH:mm'
                          )}
                        </Text>
                      </VStack>

                      {/* Points and Arrow */}
                      <VStack className='items-end'>
                        <Text size='lg' className='text-primary-500 font-bold'>
                          {business.points} pts
                        </Text>
                        <FontAwesome
                          name='chevron-right'
                          size={12}
                          color='#9CA3AF'
                        />
                      </VStack>
                    </HStack>
                  </Pressable>
                ))
              )}
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
